{"COMPONENT_KCONFIGS": "E:/esp32/v5.3.3/esp-idf/components/efuse/Kconfig;E:/esp32/v5.3.3/esp-idf/components/esp_common/Kconfig;E:/esp32/v5.3.3/esp-idf/components/esp_hw_support/Kconfig;E:/esp32/v5.3.3/esp-idf/components/esp_system/Kconfig;E:/esp32/v5.3.3/esp-idf/components/freertos/Kconfig;E:/esp32/v5.3.3/esp-idf/components/hal/Kconfig;E:/esp32/v5.3.3/esp-idf/components/log/Kconfig;E:/esp32/v5.3.3/esp-idf/components/newlib/Kconfig;E:/esp32/v5.3.3/esp-idf/components/soc/Kconfig;E:/esp32/v5.3.3/esp-idf/components/spi_flash/Kconfig", "COMPONENT_KCONFIGS_PROJBUILD": "E:/esp32/v5.3.3/esp-idf/components/bootloader/Kconfig.projbuild;E:/esp32/v5.3.3/esp-idf/components/esp_app_format/Kconfig.projbuild;E:/esp32/v5.3.3/esp-idf/components/esp_rom/Kconfig.projbuild;E:/esp32/v5.3.3/esp-idf/components/esptool_py/Kconfig.projbuild;E:/esp32/v5.3.3/esp-idf/components/partition_table/Kconfig.projbuild", "COMPONENT_SDKCONFIG_RENAMES": "E:/esp32/v5.3.3/esp-idf/components/bootloader/sdkconfig.rename;E:/esp32/v5.3.3/esp-idf/components/esp_hw_support/sdkconfig.rename;E:/esp32/v5.3.3/esp-idf/components/esp_hw_support/sdkconfig.rename.esp32c3;E:/esp32/v5.3.3/esp-idf/components/esp_system/sdkconfig.rename;E:/esp32/v5.3.3/esp-idf/components/esp_system/sdkconfig.rename.esp32c3;E:/esp32/v5.3.3/esp-idf/components/esptool_py/sdkconfig.rename;E:/esp32/v5.3.3/esp-idf/components/freertos/sdkconfig.rename;E:/esp32/v5.3.3/esp-idf/components/hal/sdkconfig.rename;E:/esp32/v5.3.3/esp-idf/components/newlib/sdkconfig.rename.esp32c3;E:/esp32/v5.3.3/esp-idf/components/spi_flash/sdkconfig.rename", "IDF_TARGET": "esp32c3", "IDF_TOOLCHAIN": "gcc", "IDF_VERSION": "5.3.3", "IDF_ENV_FPGA": "", "IDF_PATH": "E:/esp32/v5.3.3/esp-idf", "COMPONENT_KCONFIGS_SOURCE_FILE": "E:/esp32_space/hello_world/build/bootloader/kconfigs.in", "COMPONENT_KCONFIGS_PROJBUILD_SOURCE_FILE": "E:/esp32_space/hello_world/build/bootloader/kconfigs_projbuild.in"}
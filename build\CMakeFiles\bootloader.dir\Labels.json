{"sources": [{"file": "E:/esp32_space/hello_world/build/CMakeFiles/bootloader"}, {"file": "E:/esp32_space/hello_world/build/CMakeFiles/bootloader.rule"}, {"file": "E:/esp32_space/hello_world/build/CMakeFiles/bootloader-complete.rule"}, {"file": "E:/esp32_space/hello_world/build/bootloader-prefix/src/bootloader-stamp/bootloader-build.rule"}, {"file": "E:/esp32_space/hello_world/build/bootloader-prefix/src/bootloader-stamp/bootloader-configure.rule"}, {"file": "E:/esp32_space/hello_world/build/bootloader-prefix/src/bootloader-stamp/bootloader-download.rule"}, {"file": "E:/esp32_space/hello_world/build/bootloader-prefix/src/bootloader-stamp/bootloader-install.rule"}, {"file": "E:/esp32_space/hello_world/build/bootloader-prefix/src/bootloader-stamp/bootloader-mkdir.rule"}, {"file": "E:/esp32_space/hello_world/build/bootloader-prefix/src/bootloader-stamp/bootloader-patch.rule"}, {"file": "E:/esp32_space/hello_world/build/bootloader-prefix/src/bootloader-stamp/bootloader-update.rule"}], "target": {"labels": ["bootloader"], "name": "bootloader"}}
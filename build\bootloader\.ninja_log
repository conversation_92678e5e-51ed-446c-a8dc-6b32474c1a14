# ninja log v6
24	143	7766657568504252	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj	a248d9b6225eade8
29	304	7766657568544281	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj	e49125703f8b0da2
45	340	7766657568714296	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj	7cd4d1d11520591c
40	353	7766657568664286	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj	e1d239ccb1a8dbca
51	362	7766657568774282	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_gpio.c.obj	ec4944168f618ffb
9	372	7766657568344268	esp-idf/log/CMakeFiles/__idf_log.dir/log.c.obj	4f7506189f000da4
82	381	7766657569074291	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/esp_cpu_intr.c.obj	d677dd37ff453600
19	392	7766657568454258	esp-idf/log/CMakeFiles/__idf_log.dir/log_noos.c.obj	b06db8ada8ba2f53
57	402	7766657568834269	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_systimer.c.obj	c9a161318f7c76e1
101	410	7766657569264307	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/cpu_region_protect.c.obj	50a61bc11f6b52eb
15	418	7766657568404291	esp-idf/log/CMakeFiles/__idf_log.dir/log_buffers.c.obj	5a5a6b4b4713844f
91	433	7766657569174308	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj	ecf249d7c30fcbe3
35	440	7766657568614268	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj	29b8e34264e9bf6d
65	558	7766657568914282	esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj	d50c23aff9055fef
109	611	7766657569344308	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_clk_init.c.obj	451671bb0378080e
73	639	7766657568984309	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj	69a3e5cc4decf738
126	710	7766657569514306	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_init.c.obj	bc37a51cf5838e7a
144	719	7766657569704309	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_sleep.c.obj	61e9e3b5cda9d016
341	736	7766657571674279	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/chip_info.c.obj	ace0f66e0d9fe318
372	746	7766657571984298	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_fields.c.obj	99c2c2e6d7b10c1a
362	755	7766657571884293	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_table.c.obj	6f015eebc862b717
353	764	7766657571794313	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj	28e0f4c232ae9e7
410	787	7766657572364321	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj	1b54986ae34d943
117	863	7766657569434306	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_clk.c.obj	cbc6c9cae4f4ca22
382	872	7766657572074315	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_rtc_calib.c.obj	7fe768fe471caa9f
304	930	7766657571294317	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_time.c.obj	8409e50ce4ec3f3b
402	964	7766657572284296	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj	6beeda2fdf7caf1a
392	1000	7766657572184296	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_utility.c.obj	f64e0c70982218c9
419	1009	7766657572444314	esp-idf/log/liblog.a	c91a251e1ec6f52c
710	1036	7766657575365686	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj	def33b685586f2f6
736	1046	7766657575625698	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj	538ed6c366b56fba
639	1075	7766657574653121	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj	403f7c0f8cbce40a
441	1085	7766657572664314	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.obj	c4690c237a6d9416
719	1098	7766657575455704	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj	436218665de0bbd1
764	1106	7766657575905705	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32c3.c.obj	edf148e1578a185b
755	1115	7766657575815708	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj	ba45ad35a7848683
433	1123	7766657572594298	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj	9b89ba704a032b6b
612	1202	7766657574381577	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj	6922c77368e09fcd
746	1241	7766657575725703	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj	1b8100424461afb9
558	1273	7766657573841062	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj	2c01e0545bb858f2
964	1368	7766657577905721	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj	27133431dd8914c8
863	1397	7766657576895753	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj	13b146026f3eeaab
1085	1409	7766657579111487	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj	ebd46c68c6b0f87c
1046	1421	7766657578725714	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj	ee4c0dec837736d6
1098	1429	7766657579241484	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c3/bootloader_sha.c.obj	416a528d69581fb4
1106	1445	7766657579321522	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c3/bootloader_soc.c.obj	1f5faf3c83a38e57
1009	1472	7766657578355731	esp-idf/esp_rom/libesp_rom.a	fa17a62755a86f30
1202	1487	7766657580281485	esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/esp_bootloader_desc.c.obj	fdcc20a318bdca8e
872	1498	7766657576975697	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32c3.c.obj	58773b769042d869
787	1543	7766657576125713	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj	7d05f2e24f145415
1075	1553	7766657579011508	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj	5c1453439367b5a8
1036	1653	7766657578615707	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj	f4e5b54be0d2cf1f
1123	1662	7766657579491488	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj	b2152e64306d5d42
1273	1681	7766657580991484	esp-idf/hal/CMakeFiles/__idf_hal.dir/hal_utils.c.obj	93064b6712b2f37d
1241	1689	7766657580671481	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_wrap.c.obj	31a2f97f117ba99f
1368	1714	7766657581941108	esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj	41368e6ddbc6d9c7
1115	1733	7766657579411497	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c3/bootloader_esp32c3.c.obj	6b921d6705aff191
1397	1756	7766657582231137	esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32c3/efuse_hal.c.obj	993ccee650d75a5e
1543	1765	7766657583691127	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/interrupts.c.obj	70ac77e2573b061a
1498	1775	7766657583241121	esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj	cdfa2238b93eb3df
1429	1785	7766657582551141	esp-idf/hal/CMakeFiles/__idf_hal.dir/cache_hal.c.obj	f3c481b361963714
1487	1798	7766657583131150	esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj	28a4ae5f1d81053
1553	1807	7766657583798678	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/gpio_periph.c.obj	586d67e1d90e4fd5
1421	1874	7766657582471135	esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj	aab51297dbd95cc2
1653	1907	7766657584795333	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/uart_periph.c.obj	4bd646b2ec3e5f60
1757	1923	7766657585825333	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/rmt_periph.c.obj	cdaa7f1ca43a3be4
930	1942	7766657577565697	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj	8caac739e7c18545
1689	1967	7766657585145339	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/gdma_periph.c.obj	b9585435b7cf289
1472	1979	7766657582981141	esp-idf/esp_common/libesp_common.a	b03fb5144ef533f3
1662	1996	7766657584885297	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/adc_periph.c.obj	22055bc4ea584607
1409	1997	7766657582351121	esp-idf/hal/CMakeFiles/__idf_hal.dir/wdt_hal_iram.c.obj	61f0cda3e5bb5145
1733	1997	7766657585595333	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/ledc_periph.c.obj	36b30af58e3d1c25
1681	1998	7766657585075316	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/dedic_gpio_periph.c.obj	d810cba6aa09e242
1766	1998	7766657585915353	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/sdm_periph.c.obj	6985b322d69b599
1714	1999	7766657585405322	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/spi_periph.c.obj	2f9303ebe8eb00aa
1798	1999	7766657586245323	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/temperature_sensor_periph.c.obj	fe04fe7f97cb19d5
1000	2000	7766657578255713	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj	753340ac6a6996f7
1775	2000	7766657586015336	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/i2s_periph.c.obj	86f3f962ed60fb67
1786	2001	7766657586115314	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/i2c_periph.c.obj	ef1f431a22cf929a
1807	2014	7766657586335349	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/timer_periph.c.obj	262d41b6d6d5417b
1942	2023	7766657588395319	project_elf_src_esp32c3.c	41379eabcef25e07
1942	2023	7766657588395319	E:/esp32_space/hello_world/build/bootloader/project_elf_src_esp32c3.c	41379eabcef25e07
1908	2031	7766657587335317	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/twai_periph.c.obj	b05e59a0fdb90491
1874	2032	7766657587005332	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/mpi_periph.c.obj	3d34342a0a5e3580
1923	2057	7766657587485323	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/wdt_periph.c.obj	b8651ab919ef0e46
2023	2091	7766657588495334	CMakeFiles/bootloader.elf.dir/project_elf_src_esp32c3.c.obj	4cd6279a9161e6ee
1967	2123	7766657587935333	esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj	3edd0dce3ff2e9ad
1979	2125	7766657588055329	esp-idf/esp_hw_support/libesp_hw_support.a	894c438beedadd28
2125	2207	7766657589505303	esp-idf/esp_system/libesp_system.a	4a8745f9c5d686d2
2208	2308	7766657590335309	esp-idf/efuse/libefuse.a	ee4f0e88cc352499
2308	2425	7766657591345292	esp-idf/bootloader_support/libbootloader_support.a	8733c75031909e5e
2425	2497	7766657592505282	esp-idf/esp_bootloader_format/libesp_bootloader_format.a	dbdcbbb2f8f34b2
1445	2513	7766657582711103	esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/uECC_verify_antifault.c.obj	9de450b2fe660c4c
2497	2573	7766657593225293	esp-idf/spi_flash/libspi_flash.a	344553f8c1d7b7f4
2573	2650	7766657593991216	esp-idf/hal/libhal.a	38abfeb38a2a248d
2650	2720	7766657594761211	esp-idf/micro-ecc/libmicro-ecc.a	eed29c262133c2d0
2720	2822	7766657595461220	esp-idf/soc/libsoc.a	2ed56a05eac453da
2822	2885	7766657596476328	esp-idf/main/libmain.a	5bc613d7954c6145
2885	3008	7766657597106312	bootloader.elf	6e637ddc8dfc3359
3008	3296	7766657601171221	.bin_timestamp	e4daa26ed6db0ed8
3008	3296	7766657601171221	E:/esp32_space/hello_world/build/bootloader/.bin_timestamp	e4daa26ed6db0ed8
3296	3396	7766657601221237	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	8ffbb1dc58ba9748
3296	3396	7766657601221237	E:/esp32_space/hello_world/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	8ffbb1dc58ba9748

# ninja log v6
22	119	7766618135601260	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj	a248d9b6225eade8
25	196	7766618135631223	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj	e49125703f8b0da2
41	221	7766618135781215	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj	7cd4d1d11520591c
46	241	7766618135841222	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_gpio.c.obj	ec4944168f618ffb
17	276	7766618135551232	esp-idf/log/CMakeFiles/__idf_log.dir/log_noos.c.obj	b06db8ada8ba2f53
36	287	7766618135741227	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj	e1d239ccb1a8dbca
52	305	7766618135891222	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_systimer.c.obj	c9a161318f7c76e1
77	313	7766618136151229	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj	ecf249d7c30fcbe3
13	328	7766618135511247	esp-idf/log/CMakeFiles/__idf_log.dir/log_buffers.c.obj	5a5a6b4b4713844f
8	349	7766618135461243	esp-idf/log/CMakeFiles/__idf_log.dir/log.c.obj	4f7506189f000da4
30	373	7766618135681250	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj	29b8e34264e9bf6d
70	397	7766618136081246	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/esp_cpu_intr.c.obj	d677dd37ff453600
57	424	7766618135951234	esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj	d50c23aff9055fef
63	463	7766618136011247	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj	69a3e5cc4decf738
84	470	7766618136211228	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/cpu_region_protect.c.obj	50a61bc11f6b52eb
221	554	7766618137591284	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/chip_info.c.obj	ace0f66e0d9fe318
94	563	7766618136321234	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_clk_init.c.obj	451671bb0378080e
241	599	7766618137791237	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj	28e0f4c232ae9e7
111	606	7766618136491254	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_init.c.obj	bc37a51cf5838e7a
276	614	7766618138141253	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_table.c.obj	6f015eebc862b717
121	631	7766618136591254	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_sleep.c.obj	61e9e3b5cda9d016
305	638	7766618138431235	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_rtc_calib.c.obj	7fe768fe471caa9f
288	691	7766618138261267	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_fields.c.obj	99c2c2e6d7b10c1a
102	724	7766618136401252	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_clk.c.obj	cbc6c9cae4f4ca22
349	730	7766618138871249	esp-idf/log/liblog.a	c91a251e1ec6f52c
197	758	7766618137351263	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_time.c.obj	8409e50ce4ec3f3b
373	790	7766618139111261	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj	1b54986ae34d943
328	887	7766618138661244	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj	6beeda2fdf7caf1a
313	913	7766618138511242	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_utility.c.obj	f64e0c70982218c9
631	923	7766618141691255	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj	ba45ad35a7848683
563	931	7766618141011264	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj	def33b685586f2f6
599	941	7766618141371242	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj	436218665de0bbd1
607	959	7766618141451269	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj	538ed6c366b56fba
554	967	7766618140921245	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj	403f7c0f8cbce40a
639	974	7766618141761248	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32c3.c.obj	edf148e1578a185b
463	1001	7766618140011244	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj	2c01e0545bb858f2
425	1010	7766618139631235	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.obj	c4690c237a6d9416
614	1020	7766618141521276	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj	1b8100424461afb9
397	1041	7766618139351245	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj	9b89ba704a032b6b
471	1088	7766618140091244	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj	6922c77368e09fcd
730	1151	7766618142681229	esp-idf/esp_rom/libesp_rom.a	fa17a62755a86f30
724	1232	7766618142621247	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj	13b146026f3eeaab
931	1261	7766618144691231	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj	ee4c0dec837736d6
887	1269	7766618144251258	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj	27133431dd8914c8
1020	1306	7766618145581264	esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/esp_bootloader_desc.c.obj	fdcc20a318bdca8e
759	1314	7766618142971249	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32c3.c.obj	58773b769042d869
967	1322	7766618145051232	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c3/bootloader_sha.c.obj	416a528d69581fb4
974	1329	7766618145121243	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c3/bootloader_soc.c.obj	1f5faf3c83a38e57
941	1372	7766618144791234	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj	5c1453439367b5a8
1010	1379	7766618145481248	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj	b2152e64306d5d42
959	1387	7766618144971228	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj	ebd46c68c6b0f87c
691	1441	7766618142291247	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj	7d05f2e24f145415
1089	1449	7766618146261253	esp-idf/hal/CMakeFiles/__idf_hal.dir/hal_utils.c.obj	93064b6712b2f37d
1042	1498	7766618145801268	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_wrap.c.obj	31a2f97f117ba99f
923	1549	7766618144611248	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj	f4e5b54be0d2cf1f
1151	1558	7766618146891232	esp-idf/esp_common/libesp_common.a	b03fb5144ef533f3
1232	1579	7766618147701224	esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj	41368e6ddbc6d9c7
1330	1588	7766618148671226	esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj	28a4ae5f1d81053
1379	1600	7766618149171239	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/interrupts.c.obj	70ac77e2573b061a
1002	1612	7766618145401225	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c3/bootloader_esp32c3.c.obj	6b921d6705aff191
1449	1622	7766618149871226	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/adc_periph.c.obj	22055bc4ea584607
1387	1636	7766618149251242	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/gpio_periph.c.obj	586d67e1d90e4fd5
1372	1647	7766618149101237	esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj	cdfa2238b93eb3df
1441	1676	7766618149791237	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/uart_periph.c.obj	4bd646b2ec3e5f60
1261	1694	7766618147991217	esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32c3/efuse_hal.c.obj	993ccee650d75a5e
1314	1772	7766618148521218	esp-idf/hal/CMakeFiles/__idf_hal.dir/cache_hal.c.obj	f3c481b361963714
1498	1795	7766618150361245	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/dedic_gpio_periph.c.obj	d810cba6aa09e242
1306	1848	7766618148441241	esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj	aab51297dbd95cc2
791	1905	7766618143291280	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj	8caac739e7c18545
1549	1926	7766618150871230	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/gdma_periph.c.obj	b9585435b7cf289
1588	1927	7766618151261248	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/ledc_periph.c.obj	36b30af58e3d1c25
1270	1927	7766618148081261	esp-idf/hal/CMakeFiles/__idf_hal.dir/wdt_hal_iram.c.obj	61f0cda3e5bb5145
1612	1928	7766618151501234	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/sdm_periph.c.obj	6985b322d69b599
1647	1928	7766618151851243	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/temperature_sensor_periph.c.obj	fe04fe7f97cb19d5
1600	1928	7766618151381252	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/rmt_periph.c.obj	cdaa7f1ca43a3be4
913	1929	7766618144511237	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj	753340ac6a6996f7
1622	1930	7766618151601258	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/i2s_periph.c.obj	86f3f962ed60fb67
1636	1930	7766618151741232	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/i2c_periph.c.obj	ef1f431a22cf929a
1580	1930	7766618151171243	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/spi_periph.c.obj	2f9303ebe8eb00aa
1558	1930	7766618150961246	esp-idf/esp_hw_support/libesp_hw_support.a	894c438beedadd28
1676	1953	7766618152141228	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/timer_periph.c.obj	262d41b6d6d5417b
1695	1954	7766618152331230	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/mpi_periph.c.obj	3d34342a0a5e3580
1772	1955	7766618153101218	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/twai_periph.c.obj	b05e59a0fdb90491
1849	1955	7766618154871232	project_elf_src_esp32c3.c	41379eabcef25e07
1849	1955	7766618154871232	E:/esp32_space/hello_world/build/bootloader/project_elf_src_esp32c3.c	41379eabcef25e07
1795	1991	7766618153331219	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/wdt_periph.c.obj	b8651ab919ef0e46
1955	2017	7766618154931232	CMakeFiles/bootloader.elf.dir/project_elf_src_esp32c3.c.obj	4cd6279a9161e6ee
1930	2044	7766618154681225	esp-idf/esp_system/libesp_system.a	4a8745f9c5d686d2
1905	2082	7766618154431232	esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj	3edd0dce3ff2e9ad
2044	2163	7766618155821213	esp-idf/efuse/libefuse.a	ee4f0e88cc352499
2163	2299	7766618157011239	esp-idf/bootloader_support/libbootloader_support.a	8733c75031909e5e
2299	2397	7766618158371218	esp-idf/esp_bootloader_format/libesp_bootloader_format.a	dbdcbbb2f8f34b2
2397	2498	7766618159351220	esp-idf/spi_flash/libspi_flash.a	344553f8c1d7b7f4
1322	2527	7766618148601244	esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/uECC_verify_antifault.c.obj	9de450b2fe660c4c
2498	2594	7766618160361221	esp-idf/hal/libhal.a	38abfeb38a2a248d
2594	2684	7766618161321214	esp-idf/micro-ecc/libmicro-ecc.a	eed29c262133c2d0
2684	2809	7766618162221238	esp-idf/soc/libsoc.a	2ed56a05eac453da
2809	2897	7766618163471204	esp-idf/main/libmain.a	5bc613d7954c6145
2897	3072	7766618164351230	bootloader.elf	6e637ddc8dfc3359
3072	3372	7766618169061241	.bin_timestamp	e4daa26ed6db0ed8
3072	3372	7766618169061241	E:/esp32_space/hello_world/build/bootloader/.bin_timestamp	e4daa26ed6db0ed8
3372	3475	7766618169101202	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	8ffbb1dc58ba9748
3372	3475	7766618169101202	E:/esp32_space/hello_world/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	8ffbb1dc58ba9748

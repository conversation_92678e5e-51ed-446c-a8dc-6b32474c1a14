{"C_Cpp.intelliSenseEngine": "default", "idf.espIdfPathWin": "e:\\esp32\\v5.3.3\\esp-idf", "idf.toolsPathWin": "E:\\esp_tool", "idf.pythonInstallPath": "e:\\esp_tool\\tools\\idf-python\\3.11.2\\python.exe", "idf.openOcdConfigs": ["board/esp32c3-builtin.cfg"], "idf.customExtraVars": {"IDF_TARGET": "esp32c3"}, "clangd.path": "E:\\esp_tool\\tools\\esp-clang\\16.0.1-fe4f10a809\\esp-clang\\bin\\clangd.exe", "clangd.arguments": ["--background-index", "--query-driver=E:\\esp_tool\\tools\\riscv32-esp-elf\\esp-13.2.0_20240530\\riscv32-esp-elf\\bin\\riscv32-esp-elf-gcc.exe", "--compile-commands-dir=e:\\esp32_space\\hello_world\\build"], "idf.portWin": "COM3", "idf.flashType": "UART"}